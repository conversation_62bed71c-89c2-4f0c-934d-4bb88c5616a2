'use client';
import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { summarizeNote } from '../../groqAi';
import { useTheme } from '@/context/ThemeContext';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
// import { CSVLink } from 'react-csv';
// import { useDropzone } from 'react-dropzone';
import { Menu, Transition } from '@headlessui/react';
// import { ArrowUpTrayIcon} from '@heroicons/react/20/solid';
import { toast } from 'react-toastify';
import { SummaryModal} from '../components/SummaryModal';
import { CameraCapture} from '../components/CameraCapture';
import { ExportDropdown} from '../components/ExportDropdown';
import { getColorValue } from '../types/colorUtils'; // Adjust path as  // Adjust the path as necessary // Adjust the path accordingly
import { NoteColor } from '../types/notes';
import { StickyNote } from '../types/notes';
import { encryptionService } from '../utils/encryption';
import { Bell } from 'react-feather';
import VoiceInput from './VoiceInput';
import { NextApiRequest, NextApiResponse } from 'next';
// import { semanticSearch } from '../utils/semanticSearch';
// import { CollaborativeNote } from './CollaborativeNote';
import { EllipsisVerticalIcon, PencilIcon, TrashIcon, ShareIcon } from '@heroicons/react/24/outline';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);


import { DecryptedNote} from '@/utils/encryption';
// import { Bell as LucideBell } from 'lucide-react';
// NoteColor type is now imported from colorUtils

// Removed local StickyNote interface to resolve conflict with import

// Add new interfaces at top
interface Reminder {
  id: string;
  noteId: number;
  noteTitle: string;
  time: Date;
  isRead: boolean;
  content: string; // Add content property
}

const HomePage: React.FC = () => {
  const convertToStickyNote = (note: DecryptedNote | null): StickyNote | null => {
    if (!note) return null;
    
    return {
      id: note.id || 0,
      title: note.title || '',
      content: note.content || '',
      color: (note.color || 'yellow') as NoteColor,
      emoji: note.emoji || '📝',
      actionItems: Array.isArray(note.actionItems) ? note.actionItems : [],
      date: note.created_at?.toString() || new Date().toISOString(),
      created_at: note.created_at?.toString() || new Date().toISOString(),
      updated_at: note.updated_at?.toString() || new Date().toISOString(),
      summary: note.summary || "No summary available",  // Provide a default summary if it's missing
    };
  };
  const router = useRouter();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [notes, setNotes] = useState<DecryptedNote[]>([]);
  const [reminderTime, setReminderTime] = useState<Date | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeReminders, setActiveReminders] = useState<Map<string, number>>(new Map());
  const [editingNote, setEditingNote] = useState<number | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [editContent, setEditContent] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSummary, setSelectedSummary] = useState<{
    title: string;
    summary: string;
    color?: NoteColor;
  } | null>(null);
  const { theme, toggleTheme } = useTheme();
  const [] = useState(false);

  // Add new state
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [showReminders, setShowReminders] = useState(false);
  const [showReminderPopup, setShowReminderPopup] = useState(false);
  const [activeReminder, setActiveReminder] = useState<Reminder | null>(null);
  // const [activeUsers, setActiveUsers] = useState<User[]>([]);
  const NotificationBell = () => {
    const unreadCount = reminders.filter(r => !r.isRead).length;
    
    return (
      <div className="relative">
        <button
          onClick={() => setShowReminders(!showReminders)}
          className="relative p-2 rounded-full hover:bg-gray-100"
        >
          <Bell className={`h-6 w-6 ${unreadCount > 0 ? 'text-blue-500' : 'text-gray-500'}`} />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {unreadCount}
            </span>
          )}
        </button>
        
        {/* Reminders Dropdown */}
        {showReminders && (
          <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg z-50">
            <div className="p-4">
              <h3 className="text-lg font-semibold mb-2">Reminders</h3>
              {reminders.length === 0 ? (
                <p className="text-gray-500">No reminders</p>
              ) : (
                <div className="space-y-2">
                  {reminders.map(reminder => (
                    <div 
                      key={reminder.id}
                      className={`p-2 rounded ${reminder.isRead ? 'bg-gray-50' : 'bg-blue-50'}`}
                    >
                      <p className="font-medium">{reminder.noteTitle}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(reminder.time).toLocaleString()}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Add new state for tracking shared notes
  const [sharedNoteIds, setSharedNoteIds] = useState<Set<string>>(new Set());

  // Update handleReminderSet
  const handleReminderSet = async (noteData: { title: string; content: string }, date: Date) => {
    try {
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        toast.error('Please enable notifications');
        return false;
      }
  
      const reminder: Reminder = {
        id: crypto.randomUUID(),
        noteId: Date.now(),
        noteTitle: noteData.title,
        time: date,
        isRead: false,
        content: noteData.content
      };
  
      setReminders(prev => [...prev, reminder]);
      setReminderTime(date);
  
      // Schedule notification
      const timeDiff = date.getTime() - new Date().getTime();
      setTimeout(() => {
        setActiveReminder(reminder);
        setShowReminderPopup(true);
        new Notification(reminder.noteTitle, {
          body: noteData.content,
          requireInteraction: true
        });
      }, timeDiff);
  
      return true;
    } catch (error) {
      console.error('Reminder error:', error);
      return false;
    }
  };


const deleteMeetingNote = async (noteId: number, reminderId: string) => {
  try {
    setLoading(true);
    
    // Delete from Supabase
    const { error } = await supabase
      .from('sticky_notes')
      .delete()
      .eq('id', noteId);

    if (error) throw error;

    // Update local state
    setNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
    setReminders(prevReminders => prevReminders.filter(r => r.id !== reminderId));
    
    // Clear active reminder
    const timerId = activeReminders.get(reminderId);
    if (timerId) clearTimeout(timerId);
    setActiveReminders(prev => {
      const next = new Map(prev);
      next.delete(reminderId);
      return next;
    });

    setShowReminderPopup(false);
    setActiveReminder(null);
    
    toast.success('Meeting note deleted');
  } catch (error) {
    console.error('Error deleting note:', error);
    toast.error('Failed to delete note');
  } finally {
    setLoading(false);
  }
};

const ReminderPopup = () => {
  if (!showReminderPopup || !activeReminder) return null;

  const handleDelete = async () => {
    await deleteMeetingNote(activeReminder.noteId, activeReminder.id);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
        <h3 className="text-lg font-bold mb-2">{activeReminder.noteTitle}</h3>
        <p className="mb-4">{activeReminder.content}</p>
        <div className="flex justify-end gap-2">
          <button
            onClick={() => setShowReminderPopup(false)}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Keep Note
          </button>
          <button
            onClick={handleDelete}
            disabled={loading}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            {loading ? 'Deleting...' : 'Delete Note'}
          </button>
        </div>
      </div>
    </div>
  );
};

  // Check if the user is authenticated on initial render
  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;
        if (!session) {
          router.push('/auth');
        }
      } catch (err) {
        console.error('Session check error:', err);
        router.push('/auth');
      }
    };

    checkSession();
  }, [router]);

  const createStickyNote = async (noteData: { title: string; content: string }) => {
    try {
      setLoading(true);

      // Validate input
      if (!noteData.title.trim() && !noteData.content.trim()) {
        toast.error('Please enter a title or content for your note');
        return;
      }

      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user?.id) {
        toast.error('Please sign in to create notes');
        throw new Error('User not authenticated');
      }

      // Simplified approach - create a basic note first
      console.log('Creating simple note...');

      // Get AI analysis with fallback
      let aiAnalysis;
      try {
        aiAnalysis = await summarizeNote(noteData.content);
        console.log('AI Analysis successful');
      } catch (aiError) {
        console.warn('AI analysis failed, using defaults');
        aiAnalysis = {
          summary: noteData.content.substring(0, 100) + (noteData.content.length > 100 ? '...' : ''),
          suggestedColor: 'yellow' as const,
          mood: 'neutral',
          emoji: '📝',
          categories: [],
          priority: 1,
          actionItems: []
        };
      }

      // Encrypt note for privacy
      let encryptedNote;
      try {
        encryptedNote = encryptionService.encryptNote(
          noteData.title,
          noteData.content,
          user.id
        );
        console.log('Encryption successful');
      } catch (encryptError) {
        console.error('Encryption failed:', encryptError);
        toast.error('Failed to encrypt note');
        throw encryptError;
      }

      // Create encrypted note object
      const newNote = {
        user_id: user.id,
        encrypted_title: encryptedNote.encrypted_title,
        encrypted_content: encryptedNote.encrypted_content,
        color: aiAnalysis.suggestedColor || 'yellow',
        emoji: aiAnalysis.emoji || '📝',
        mood: aiAnalysis.mood || 'neutral',
        priority: aiAnalysis.priority?.toString() || '1',
        ai_categories: aiAnalysis.categories || [],
        summary: aiAnalysis.summary || '',
        is_archived: false,
        reminder_time: reminderTime?.toISOString() || null
      };

      console.log('Inserting note:', newNote);

      // Save to database
      const { data: savedNote, error } = await supabase
        .from('sticky_notes')
        .insert([newNote])
        .select()
        .single();

      if (error) {
        console.error('Supabase error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        toast.error(`Database error: ${error.message}`);
        throw error;
      }

      // Convert saved note to decrypted format for immediate display
      const decryptedNote: DecryptedNote = {
        id: savedNote.id,
        title: noteData.title, // Use original data for immediate display
        content: noteData.content, // Use original data for immediate display
        color: savedNote.color,
        emoji: savedNote.emoji,
        mood: savedNote.mood,
        priority: savedNote.priority,
        is_archived: savedNote.is_archived,
        created_at: savedNote.created_at,
        updated_at: savedNote.updated_at,
        ai_categories: savedNote.ai_categories,
        summary: savedNote.summary,
        user_id: savedNote.user_id,
        reminder_time: savedNote.reminder_time ? new Date(savedNote.reminder_time) : null,
        actionItems: false
      };

      // Update state immediately with decrypted note
      setNotes(prevNotes => [decryptedNote, ...prevNotes]);
      setTitle('');
      setContent('');
      setReminderTime(null); // Reset reminder after creating note

      toast.success('Note created successfully!');
      return decryptedNote;

    } catch (error) {
      console.error('Error creating note:', error);
      toast.error('Failed to create note. Please try again.');
    } finally {
      setLoading(false);
    }
  };

// Fetch sticky notes from Supabase
const fetchNotes = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data: encryptedNotes, error } = await supabase
      .from('sticky_notes')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false }); // Sort newest first

    if (error) throw error;

    const decryptedNotes = encryptedNotes.map((note): DecryptedNote | null => {
      try {
        // Decrypt the note for display
        const decrypted = encryptionService.decryptNote(note);
        return {
          id: note.id,
          title: decrypted.title,
          content: decrypted.content,
          color: note.color || 'yellow',
          emoji: note.emoji || '📝',
          mood: note.mood || 'neutral',
          priority: note.priority || '1',
          summary: note.summary || '',
          ai_categories: note.ai_categories || [],
          created_at: note.created_at,
          updated_at: note.updated_at,
          // Add the missing required properties
          user_id: note.user_id,
          actionItems: note.actionItems || [],
          reminder_time: note.reminder_time || null,
          is_archived: note.is_archived || false
        };
      } catch (error) {
        console.error('Failed to decrypt note:', error);
        return null;
      }
    }).filter((note): note is DecryptedNote => note !== null);
    
    setNotes(decryptedNotes);
  } catch (error) {
    console.error('Error fetching notes:', error);
  }
};

useEffect(() => {
  fetchNotes();
}, []);


  const toggleNoteSharing = async (noteId: string) => {
    try {
      setSharedNoteIds(prev => {
        const newSet = new Set(prev);
        if (newSet.has(noteId)) {
          newSet.delete(noteId);
          toast.success('Note sharing disabled');
        } else {
          newSet.add(noteId);
          toast.success('Note sharing enabled');
        }
        return newSet;
      });
    } 
    catch  {
      toast.error('Failed to toggle note sharing');
    }
  };

  const deleteStickyNote = async (id: number) => {
    setLoading(true);

    try {
      const { error: deleteError } = await supabase
        .from('sticky_notes')
        .delete()
        .eq('id', id);

      if (deleteError) throw new Error(deleteError.message);

      setNotes(notes.filter((note) => note.id !== id));
    } 
    // catch (err) {
    //   const errorMessage = (err as Error).message || 'An error occurred';
    //   console.error(errorMessage);
    // } 
    finally {
      setLoading(false);
    }
  };

  const updateStickyNote = async (noteId: number, newColor?: NoteColor) => {
      try {
        setLoading(true);
  
        const updateData: Partial<DecryptedNote> = {};
        
        if (newColor) {
          // If only updating color
          updateData.color = newColor;
        } else {
          // If updating content and title
          if (!editTitle.trim() || !editContent.trim()) {
            alert('Title and content cannot be empty');
            return;
          }
          
          const updatedSummary = await summarizeNote(editContent);
          updateData.title = editTitle.trim();
          updateData.content = editContent.trim();
          updateData.summary = typeof updatedSummary === 'string' 
            ? updatedSummary 
            : updatedSummary.summary || 'Summary unavailable';
          if (typeof updatedSummary === 'object' && updatedSummary.suggestedColor) {
            updateData.color = updatedSummary.suggestedColor;
          }
        }
  
        const { error: updateError } = await supabase
          .from('sticky_notes')
          .update(updateData)
          .eq('id', noteId);
  
        if (updateError) throw updateError;
  
        // Update local state
        setNotes(notes.map(note => 
          note.id === noteId ? { ...note, ...updateData } : note
        ));
  
        if (!newColor) {
          setEditingNote(null);
          setEditTitle('');
          setEditContent('');
        }
      } catch (error) {
        console.error('Error updating note:', error);
        alert('Failed to update note. Please try again.');
      } finally {
        setLoading(false);
      }
    };


  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/auth');
  };


  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return; // Exit if no valid destination
    
    const items = Array.from(notes);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setNotes(items);
  };

  const filteredNotes = notes.filter(note => 
    (note?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false) || 
    (note?.content?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false)
  );

  

  // Adjust font size for mobile screens
  return (
    <div className={`min-h-screen ${theme === 'dark'
      ? 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900'
      : 'bg-gradient-to-br from-indigo-50 via-white to-cyan-50'
    } transition-all duration-500`}>

      {/* Modern Header */}
      <div className="sticky top-0 z-50 backdrop-blur-xl bg-white/10 dark:bg-black/10 border-b border-white/20 dark:border-white/10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="w-14 h-14 bg-gradient-to-r from-violet-500 via-purple-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-2xl">
                  <span className="text-2xl">🧠</span>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
                  StickyAI
                </h1>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                  ✨ AI-Powered Smart Notes
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <CameraCapture
                onCapture={(text) => setContent(text)}
              />
              <ExportDropdown notes={notes.map(convertToStickyNote).filter(note => note !== null)} />
              <NotificationBell />

              <button
                onClick={toggleTheme}
                className="p-3 rounded-2xl bg-white/20 dark:bg-black/20 backdrop-blur-sm hover:bg-white/30 dark:hover:bg-black/30 transition-all duration-200 border border-white/30"
              >
                {theme === 'light' ? '🌙' : '☀️'}
              </button>

              <button
                onClick={handleSignOut}
                className="px-4 py-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-2xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 font-medium shadow-lg"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Create Note Form - Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <div className="bg-white/70 dark:bg-black/40 backdrop-blur-2xl rounded-3xl p-6 shadow-2xl border border-white/30 dark:border-white/10">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-emerald-400 via-cyan-400 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
                    <span className="text-2xl">✨</span>
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-gray-800 dark:text-white">
                      Create Note
                    </h2>
                    <p className="text-xs text-gray-500 dark:text-gray-400">AI-powered</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Note title..."
                      className="w-full p-4 bg-white/50 dark:bg-black/20 border-0 rounded-2xl text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:bg-white dark:focus:bg-black/40 focus:ring-2 focus:ring-violet-500/50 transition-all duration-300 backdrop-blur-sm"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                    />
                  </div>

                  <div className="relative">
                    <textarea
                      placeholder="What's on your mind? AI will help organize your thoughts..."
                      className="w-full p-4 bg-white/50 dark:bg-black/20 border-0 rounded-2xl min-h-[120px] text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:bg-white dark:focus:bg-black/40 focus:ring-2 focus:ring-violet-500/50 transition-all duration-300 resize-none backdrop-blur-sm"
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                    />
                  </div>

                  {/* Reminder Section */}
                  <div className="relative">
                    <input
                      type="datetime-local"
                      className="w-full p-4 bg-white/50 dark:bg-black/20 border-0 rounded-2xl text-gray-800 dark:text-white focus:bg-white dark:focus:bg-black/40 focus:ring-2 focus:ring-violet-500/50 transition-all duration-300 backdrop-blur-sm"
                      value={reminderTime
                        ? new Date(reminderTime.getTime() - reminderTime.getTimezoneOffset() * 60000)
                            .toISOString()
                            .slice(0, 16)
                        : ''
                      }
                      onChange={(e) => e.target.value && handleReminderSet({ title, content }, new Date(e.target.value))}
                      min={new Date().toISOString().slice(0, 16)}
                    />
                    {reminderTime && (
                      <button
                        onClick={() => setReminderTime(null)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-red-400 hover:text-red-500 transition-colors"
                      >
                        ✕
                      </button>
                    )}
                  </div>

                  {/* Voice Input */}
                  <div className="flex gap-3">
                    <VoiceInput
                      onTranscriptionComplete={async (noteData) => {
                        try {
                          await createStickyNote(noteData);
                          toast.success('Voice note created');
                        } catch {
                          toast.error('Failed to create voice note');
                        }
                      }}
                    />
                  </div>

                  {/* Create Button */}
                  <button
                    onClick={async () => {
                      try {
                        await createStickyNote({ title, content });
                      } catch (error) {
                        console.error('Button click error:', error);
                      }
                    }}
                    className="w-full p-4 bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 text-white rounded-2xl hover:from-violet-700 hover:via-purple-700 hover:to-blue-700 transform hover:scale-[1.02] transition-all duration-300 shadow-2xl hover:shadow-violet-500/25 font-bold text-lg flex items-center justify-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent"></div>
                        Creating Magic...
                      </>
                    ) : (
                      <>
                        <span className="text-xl">✨</span>
                        Create Note
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>

          {/* Notes Grid */}
          <div className="lg:col-span-3">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-2xl">📝</span>
                </div>
                <div>
                  <h2 className="text-3xl font-black text-gray-800 dark:text-white">Your Notes</h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{notes.length} notes created</p>
                </div>
              </div>

              <div className="relative">
                <input
                  type="text"
                  placeholder="🔍 Search your notes..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-80 p-4 pl-12 bg-white/50 dark:bg-black/20 border-0 rounded-2xl text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:bg-white dark:focus:bg-black/40 focus:ring-2 focus:ring-violet-500/50 transition-all duration-300 backdrop-blur-sm"
                />
                <div className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400">
                  🔍
                </div>
              </div>
            </div>

            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable
                droppableId="notes"
                direction="horizontal"
                isDropDisabled={false}
                isCombineEnabled={false}
                ignoreContainerClipping={false}
              >
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                  >
                    {filteredNotes.map((note, index) => (
                      <Draggable key={note.id} draggableId={String(note.id)} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="group relative overflow-hidden"
                            style={{
                              ...provided.draggableProps.style,
                            }}
                          >
                            {/* Modern Note Card */}
                            <div className="relative bg-white/80 dark:bg-black/40 backdrop-blur-2xl rounded-3xl p-6 shadow-2xl hover:shadow-violet-500/20 transform hover:scale-[1.02] transition-all duration-500 border border-white/30 dark:border-white/10 min-h-[320px] cursor-pointer overflow-hidden">

                              {/* Gradient Border Effect */}
                              <div className="absolute inset-0 bg-gradient-to-r from-violet-500/20 via-purple-500/20 to-blue-500/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                              {/* Color Accent */}
                              <div
                                className="absolute top-0 left-0 w-full h-2 rounded-t-3xl"
                                style={{ background: getColorValue(note.color || 'yellow') }}
                              ></div>
                              {/* Content */}
                              <div className="relative z-10 p-6 h-full flex flex-col">
                                {editingNote === note.id ? (
                                  // Edit mode
                                  <div className="h-full flex flex-col">
                                    <input
                                      type="text"
                                      value={editTitle}
                                      onChange={(e) => setEditTitle(e.target.value)}
                                      className="mb-4 p-3 bg-white/50 dark:bg-black/20 border-0 rounded-2xl text-gray-800 dark:text-white font-bold text-lg backdrop-blur-sm"
                                      placeholder="Note title"
                                    />
                                    <textarea
                                      value={editContent}
                                      onChange={(e) => setEditContent(e.target.value)}
                                      className="flex-1 p-3 bg-white/50 dark:bg-black/20 border-0 rounded-2xl text-gray-700 dark:text-gray-300 resize-none backdrop-blur-sm"
                                      placeholder="Note content"
                                    />
                                    <div className="flex gap-3 mt-4">
                                      <button
                                        onClick={() => note.id !== undefined && updateStickyNote(note.id)}
                                        className="flex-1 px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-2xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg"
                                      >
                                        ✅ Save
                                      </button>
                                      <button
                                        onClick={() => setEditingNote(null)}
                                        className="flex-1 px-4 py-3 bg-gray-500 text-white rounded-2xl font-medium hover:bg-gray-600 transition-all duration-200 shadow-lg"
                                      >
                                        ❌ Cancel
                                      </button>
                                    </div>
                                  </div>
                                ) : (
                                  // View mode
                                  <div className="h-full flex flex-col">

                                    {/* Menu button */}
                                    <div className="absolute top-4 right-4 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                      <Menu>
                                        <Menu.Button className="p-2 hover:bg-white/20 dark:hover:bg-black/20 rounded-2xl backdrop-blur-sm transition-all duration-200">
                                          <EllipsisVerticalIcon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                                        </Menu.Button>
                                        <Transition
                                          enter="transition duration-200 ease-out"
                                          enterFrom="transform scale-95 opacity-0"
                                          enterTo="transform scale-100 opacity-100"
                                          leave="transition duration-150 ease-in"
                                          leaveFrom="transform scale-100 opacity-100"
                                          leaveTo="transform scale-95 opacity-0"
                                        >
                                          <Menu.Items className="absolute right-0 mt-2 w-44 bg-white/90 dark:bg-black/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-white/10 z-50 overflow-hidden">
                                            <Menu.Item>
                                              {({ active }) => (
                                                <button
                                                  onClick={() => {
                                                    setEditingNote(note.id ?? null);
                                                    setEditTitle(note.title);
                                                    setEditContent(note.content);
                                                  }}
                                                  className={`${
                                                    active ? 'bg-violet-500/10' : ''
                                                  } flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-violet-500/10 transition-colors duration-200`}
                                                >
                                                  <PencilIcon className="w-4 h-4 mr-3" />
                                                  ✏️ Edit
                                                </button>
                                              )}
                                            </Menu.Item>
                                            <Menu.Item>
                                              {({ active }) => (
                                                <button
                                                  onClick={() => note.id !== undefined && deleteStickyNote(note.id)}
                                                  className={`${
                                                    active ? 'bg-red-500/10' : ''
                                                  } flex items-center w-full px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-500/10 transition-colors duration-200`}
                                                >
                                                  <TrashIcon className="w-4 h-4 mr-3" />
                                                  🗑️ Delete
                                                </button>
                                              )}
                                            </Menu.Item>
                                            <Menu.Item>
                                              {({ active }) => (
                                                <button
                                                  onClick={() => note.id && toggleNoteSharing(String(note.id))}
                                                  className={`${
                                                    active ? 'bg-blue-500/10' : ''
                                                  } flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-500/10 transition-colors duration-200`}
                                                >
                                                  <ShareIcon className="w-4 h-4 mr-3" />
                                                  {sharedNoteIds.has(String(note.id)) ? '🔒 Stop Sharing' : '🔗 Share'}
                                                </button>
                                              )}
                                            </Menu.Item>
                                    </Menu.Items>
                                            </Transition>
                                          </Menu>
                                        </div>

                                        {/* Header with emoji and title */}
                                        <div className="flex items-start gap-4 mb-4">
                                          <div
                                            className="w-14 h-14 rounded-2xl flex items-center justify-center text-2xl shadow-lg"
                                            style={{ background: getColorValue(note.color || 'yellow') }}
                                          >
                                            {note.emoji}
                                          </div>
                                          <div className="flex-1">
                                            <h3 className="font-black text-xl text-gray-800 dark:text-white leading-tight mb-1">
                                              {note.title}
                                            </h3>
                                            <div className="flex items-center gap-3">
                                              <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                                                {note.created_at ? new Date(note.created_at).toLocaleDateString() : 'Today'}
                                              </span>
                                              {note.priority && parseInt(note.priority.toString()) > 3 && (
                                                <span className="text-xs bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full font-bold shadow-lg">
                                                  🔥 High Priority
                                                </span>
                                              )}
                                            </div>
                                          </div>
                                        </div>

                                        {/* Categories */}
                                        {note.ai_categories && note.ai_categories.length > 0 && (
                                          <div className="flex flex-wrap gap-2 mb-4">
                                            {note.ai_categories.map((category: string, index: number) => (
                                              <span
                                                key={`${category}-${index}`}
                                                className="text-xs bg-gradient-to-r from-violet-500/20 to-purple-500/20 text-violet-700 dark:text-violet-300 px-3 py-1 rounded-full font-medium border border-violet-200 dark:border-violet-700"
                                              >
                                                #{category}
                                              </span>
                                            ))}
                                          </div>
                                        )}

                                        {/* Content */}
                                        <div className="flex-1 mb-4">
                                          <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed line-clamp-4">
                                            {note.content}
                                          </p>
                                        </div>

                                        {/* Summary Section */}
                                        {note.summary && (
                                          <div
                                            onClick={() => setSelectedSummary({
                                              title: note.title,
                                              summary: note.summary || 'No summary available',
                                              color: note.color as NoteColor
                                            })}
                                            className="mt-auto p-4 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl cursor-pointer hover:from-blue-100/50 hover:to-purple-100/50 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30 transition-all duration-300 border border-blue-200/30 dark:border-blue-700/30"
                                          >
                                            <div className="flex items-center gap-2 mb-2">
                                              <span className="text-lg">🤖</span>
                                              <p className="text-xs font-bold text-blue-600 dark:text-blue-400">AI Summary</p>
                                            </div>
                                            <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2 font-medium">
                                              {note.summary}
                                            </p>
                                          </div>
                                        )}

                                        {/* Color picker at bottom */}
                                        <div className="flex justify-center gap-3 mt-4 pt-4 border-t border-white/20 dark:border-white/10">
                                          {['yellow', 'blue', 'green', 'pink', 'purple'].map((color) => (
                                            <button
                                              key={color}
                                              onClick={() => note.id !== undefined && updateStickyNote(note.id, color as NoteColor)}
                                              className={`w-8 h-8 rounded-full border-3 hover:scale-125 transition-all duration-200 shadow-lg ${
                                                note.color === color ? 'border-white scale-125 shadow-xl' : 'border-white/50'
                                              }`}
                                              style={{ background: getColorValue(color as NoteColor) }}
                                            />
                                          ))}
                                        </div>
                                      </div>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
        
      </div>

      {selectedSummary && (
        <SummaryModal
          isOpen={!!selectedSummary}
          onClose={() => setSelectedSummary(null)}
          title={selectedSummary.title}
          summary={selectedSummary.summary}
          color={selectedSummary.color}
        />
      )}
      <ReminderPopup />
    </div>
  );
};

export default HomePage;
